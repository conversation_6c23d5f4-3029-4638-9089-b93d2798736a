// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

        // let E = ' <E> ';
        // let l = ' <l> ';
        // let logText = ["\n %c %c %c PixiJS " + E + " - ✰ " + l + " ✰  %c  %c  http://www.pixijs.com/  %c %c ♥%c♥%c♥ \n\n", "background: #ff66a5; padding:5px 0;", "background: #ff66a5; padding:5px 0;", "color: #ff66a5; background: #030307; padding:5px 0;", "background: #ff66a5; padding:5px 0;", "background: #ffc3dc; padding:5px 0;", "background: #ff66a5; padding:5px 0;", "color: #ff2424; background: #fff; padding:5px 0;", "color: #ff2424; background: #fff; padding:5px 0;", "color: #ff2424; background: #fff; padding:5px 0;"];
        // console.log(...logText);

import AudioManager from "./Base/Components/AudioManager";
import CpSDK from "./Base/CpSDK";

export default class LocalUtils {

    public static rootGameManager: cc.Component = null;

    private static _singleSoundList: string[] = [];

    private static _section = 0;

    private static globalTimeScale = 1;
    public static SetGlobalTimeScale(scale: number) {
        LocalUtils.globalTimeScale = scale;
        // @ts-ignore
        if(!cc.director.__calculateDeltaTime) {
            // @ts-ignore
            cc.director.__calculateDeltaTime = cc.director.calculateDeltaTime
            // @ts-ignore
            cc.director.calculateDeltaTime = function (...args) {
                this.__calculateDeltaTime(...args)
                this._deltaTime *= LocalUtils.globalTimeScale
            }
        }
    }

    /*
    cc.director.calculateDeltaTime = function (now) {
        if (!now) now = performance.now();
        this._deltaTime = now > this._lastUpdate ? (now - this._lastUpdate) / 1000 : 0;
        if (CC_DEBUG && (this._deltaTime > 1))
            this._deltaTime = 1 / 60.0;
        this._lastUpdate = now;
        this._deltaTime = this._deltaTime * 3;
    }
    */
    /**
     * 更改节点的父节点，不改变绝对位置
     * @param {cc.Node} node 节点
     * @param {cc.Node} newParent 新的父节点
     */
    public static ChangeParentWithoutMoving(node: cc.Node, newParent: cc.Node) {
        let worldPos: cc.Vec3 = node.parent.convertToWorldSpaceAR(node.position);
        node.parent = newParent;
        node.setPosition(node.parent.convertToNodeSpaceAR(worldPos));
    }

    public static FollowNode(node: cc.Node, target: cc.Node, offset = cc.v2()) {
        let pos = node.parent.convertToNodeSpaceAR(target.convertToWorldSpaceAR(cc.v2()));
        cc.Vec2.add(pos, pos, offset);
        node.setPosition(pos);
    }

    /**
     * 实例化对象
     * @param {cc.Node} parent 挂载的父节点
     * @param {cc.Prefab} prefab 预制体
     * @param {?cc.Vec3} [offset] 坐标偏移
     * @returns {cc.Node}
     */
    public static GenerateNode(parent: cc.Node, prefab: cc.Prefab, offset?: cc.Vec3): cc.Node {
        let node: cc.Node = cc.instantiate(prefab);
        node.parent = parent;
        if(offset) {
            node.setPosition(offset);
        // } else {
            // node.setPosition(prefab.data.position);
        }
        return node;
    }

    public static CloneNode(srcNode: cc.Node, offset = cc.v3(), isDestroySrc?: boolean): cc.Node {
        let node: cc.Node = cc.instantiate(srcNode);
        node.parent = srcNode.parent;
        let pos = cc.v3();
        cc.Vec3.add(pos, srcNode.position, offset);
        node.setPosition(pos);
        if(isDestroySrc) { srcNode.destroy(); }
        return node;
    }
    
    public static RandomAMember<T>(members: Array<T>): T {
        if(members.length == 0) { return null; }
        let index: number = Math.floor(members.length * Math.random());
        return members[index];
    }  

    public static LengthOfArray<T>(arr: Array<T>) {
        let count = 0;
        arr.forEach((e)=>{
            if(e != undefined && e != null) { count ++;}
        });
        return count;
    }

    public static FindAllChildrenOfNode(node: cc.Node, findName = '', isFronName = false): cc.Node[] {
        let childrenList = [];
        node.children.forEach((e)=>{
            if(findName == '' || (findName == e.name) || (isFronName && node.name.slice(0, findName.length) == findName)) {
                childrenList = childrenList.concat(e);
            }
            childrenList = childrenList.concat(...this.FindAllChildrenOfNode(e, findName, isFronName));
        });
        return childrenList;
    }

    public static AllChildrenCountOfNode(node: cc.Node, findName = '', isFronName = false): number {
        let childrenCount = 0;
        node.children.forEach((e)=>{
            if(findName == '' || (findName == e.name) || (isFronName && node.name.slice(0, findName.length) == findName)) {
                childrenCount += 1
            }
            childrenCount += this.AllChildrenCountOfNode(e, findName, isFronName);
        });
        return childrenCount;
    }

    // 不重复地 push 进数组
    public static PushWithoutRepeat<T>(arr: Array<T>, element: T) {
        // let findResult = arr.find((e)=>{
        //     return e == element;
        // });
        // if(!findResult) {
        //     arr.push(element);
        // }
        arr.includes(element) || arr.push(element);
    }

    public static ArrayForEach<T>(arr: Array<T>, callback: (value: T, index: number)=>void, filter: (value: T)=>boolean = null, getter: (value: T, rtn: any)=>any = null, getterInitialValue?: any) {
        let rtn = undefined;
        arr.forEach((e, i)=>{
            if(filter && !filter(e)) { return; }
            callback(e, i);
            if(getter) {
                if(i == 0 && getterInitialValue != undefined) {
                    rtn = getterInitialValue;
                } else {
                    rtn = getter(rtn, e);
                }
            }
        });
        return rtn;
    }

    // remark == 'next' || remark == 'download' || remark == 'automatic_jump'
    // next: 按钮上有next，continue字样，表述继续游戏意思；download：表述按钮上有download字样；utomatic_jump：表示自动跳转情况的下载；
    public static CpSDKDownload(remark: 'next' | 'again' | 'download' | 'automatic_jump' | string = 'download') {
        CpSDK.ClickDownloadBar(1, remark);
    }
    // 接口remark参数必须传
    // again: 点击再来一次，并重新开始试玩；如果是下载则不调用；start：每个关卡用户操作，只有一个关卡，第一个参数-1；如果多个关卡，第一个参数是对应的关卡数；
    // end：每个关卡结束，只有一个关卡，第一个参数-1；如果多个关卡，第一个参数是对应的关卡数
    public static CpSDKClick(remark : 'again' | 'start' | 'end' = 'start', level = -1) {
        CpSDK.ClickArea(level, remark);
    }
    // 结束界面调用
    public static CpSDKEnd(isWin: boolean = true) {
        CpSDK.gameEnding(isWin ? 'win': 'lose');
        CpSDK.GameEnd(isWin);
    }
    
    public static CpSDKEnterNext() {
        this._section += 1;
        CpSDK.EnterSection(this._section);
    }
    
    public static RGTouch(e: cc.Event.EventTouch, type: 'start' | 'end') {
        if(type == 'start') {
            window["RG_TouchStart"] && window["RG_TouchStart"](e.getLocationX(),e.getLocationY());
        } else if(type == 'end') {
            window["RG_TouchEnd"] && window["RG_TouchEnd"](e.getLocationX(),e.getLocationY());
        }
    }
    
    public static PlaySoundDelay(delay: number, name: string, isLoop?: boolean, volume = 1) {
        this.rootGameManager.scheduleOnce(()=>{
            this.PlaySound(name, isLoop, volume);
        }, delay);
    }
    
    /**
     * 播放音效
     * @param {string} name 音效名
     * @param {?boolean} [isLoop] 是否循环 （true: 循环 false: 不循环）
     */
    public static PlaySound(name: string, isLoop?: boolean, volume = 1) {
        // console.log(`🔊play sound: ${name}`);
        AudioManager.play(name, volume, isLoop);
    }
    
    public static StopSound(name: string) {
        // console.log(`🔊stop sound: ${name}`);
        AudioManager.stop(name);
    }
    
    /**
     * 播放独立音效（在短时间内不会多次播放）
     * @param {string} name 音效名
     * @param {?boolean} [isLoop] 是否循环 （true: 循环 false: 不循环）
     */
    public static PlaySingleSound(name: string, isLoop?: boolean, volume = 1) {
        let sound = this._singleSoundList.find((e)=>{
            if(e == name) { return true; }
        });
        if(!sound) {
            this.PlaySound(name, isLoop, volume);
            this._singleSoundList.push(name);
            AudioManager.instance.scheduleOnce(()=>{
                let index = this._singleSoundList.indexOf(name);
                this._singleSoundList.splice(index, 1);
            }, 0.01);
        }
    }
    
    public static addShakerEffect(node: cc.Node , totalTime: number, onComplitedCallback?: Function){
        if (totalTime === null && node === null){
            return;
        }
        let actionName = 'ShakerActionName';

        let init_x = 0       //[[初始位置x]]
        let init_y = 0       //[[初始位置y]]
        let diff_x = 0       //[[偏移量x]]
        let diff_y = 0       //[[偏移量y]]
        let diff_max = 10     //[[最大偏移量]]
        let interval = 0.01  //[[震动频率]]
        // totalTime = 0    //[[震动时间]]
        let time = 0         //[[计时器]]

        let target = node;
        init_x = node.getPosition().x;
        init_y = node.getPosition().y;

        function _callback() {
            // console.log(`shaking ! pos: (${target.position.x}, ${target.position.y})`);
            if (time >= totalTime) {
                if(onComplitedCallback) {
                    onComplitedCallback();
                }
                target.stopAction(target[actionName]);
                target[actionName] = null;
                target.setPosition(init_x, init_y);
                LocalUtils.rootGameManager.unschedule(_callback);
                return;
            }
            time = time + interval;
            diff_x = Math.random()*(diff_max + diff_max+1)-diff_max;
            diff_y = Math.random()*(diff_max + diff_max+1)-diff_max;
            target.setPosition(init_x + diff_x, init_y + diff_y);
            // console.log(`shaking ! new pos: (${target.position.x}, ${target.position.y})`);
        }

        target[actionName] = LocalUtils.rootGameManager.schedule(_callback, interval);
    }

    public static addShakerTween<T extends ShakerData>(node: cc.Node, onComplitedCallback?: Function, shakerData?: T) {
        let defaultShakerData: ShakerData = {srcX: 0, srcY: 0, offsetMaxX: 20, offsetMaxY: 20, offsetRangeRatio: 0.3, interval: 0.03, totalTime: 0.3};
        shakerData = {...defaultShakerData, ...shakerData};

        if(node === null || node === undefined) {
            return;
        }
        let init_x = shakerData.srcX;       //[[初始位置x]]
        let init_y = shakerData.srcY;       //[[初始位置y]]
        let x = 0;
        let y = 0;
        let diff_x = 0;       //[[偏移量x]]
        let diff_y = 0;       //[[偏移量y]]
        let diffRangeRatio = shakerData.offsetRangeRatio;
        let diff_max_x = shakerData.offsetMaxX;     //[[最大偏移量]]
        let diff_max_y = shakerData.offsetMaxY;     //[[最大偏移量]]
        let interval = shakerData.interval;         //[[震动频率]]
        let totalTime = shakerData.totalTime;       //[[震动时间]]
        let time = 0;         //[[计时器]]

        let angle = Math.random() * 360;

        let v2 = LocalUtils.AngleToVec2(angle);

        // diff_x = -diff_max_x + Math.random() * diff_max_x * 2;
        // diff_y = -diff_max_y + Math.random() * diff_max_y * 2;
        // x = init_x + diff_x;
        // y = init_y + diff_y;
        
        let diff = LocalUtils.GetRectPosFromDir(v2, diff_max_x * (1 - Math.random() * diffRangeRatio), diff_max_y * (1 - Math.random() * diffRangeRatio));
        diff_x = diff.x;
        diff_y = diff.y;
        x = init_x + diff_x;
        y = init_y + diff_y;
        
        let repeatTimes = Math.floor(totalTime / interval) - 1;
        if(repeatTimes < 0) repeatTimes = 0;
        // console.log(`[addShakerTween] init_x: ${init_x}, diff_max_x: ${diff_max_x}`);
        
        let tween = cc.tween(node).call(()=>{
            angle = angle + 180 + (-60 + Math.random() * 120);
            while(angle >= 360) {
                angle -= 360;
            }
            while(angle < 0) {
                angle += 360;
            }
            v2 = LocalUtils.AngleToVec2(angle);
            let diff = LocalUtils.GetRectPosFromDir(v2, diff_max_x * (1 - Math.random() * diffRangeRatio), diff_max_y * (1 - Math.random() * diffRangeRatio));
            diff_x = diff.x;
            diff_y = diff.y;
            x = init_x + diff_x;
            y = init_y + diff_y;
            cc.tween(node).to(interval * 0.95, {position: cc.v3(x, y)}).start();
            // console.log(`x: ${x}, y: ${y}, node.pos: [${node.x}, ${node.y}]`);
        }).delay(interval).union().repeat(repeatTimes).to(interval, {position: cc.v3(init_x, init_y)}).call(()=>{
            onComplitedCallback && onComplitedCallback();
        }).start();

        return tween;
    }

    // 解一元二次方程组，已知条件：过两点坐标、对称轴x的坐标
    // a = (y1 - y2) / [-对称轴x * 2 * (x1 - x2) + (x1^ - x2^)]
    // b = -对称轴x * a * 2
    // c = y1 - (a * x1^ + b * x1)
    // 或
    // c = y2 - (a * x2^ + b * x2)
    public static ParabolicEquation<Out extends OutParabolicEquation>(out: Out, x1: number, x2: number, x_mid: number, y1: number, y2: number) {
        out.a = (y1 - y2) / (-x_mid * 2 * (x1 - x2) + (Math.pow(x1, 2) - Math.pow(x2, 2)));
        out.b = -x_mid * out.a * 2;
        out.c = y1 - (out.a * Math.pow(x1, 2) + out.b * x1);
    }

    // 已知 顶点的 y 坐标
    // 顶点式 y = a(x - h)^2 + k;  和标准式的对应关系：b = -2ah;  c = ah^2 +k
    // s1 = √((y1 - k) / (y2 - k))
    // s2 = -√((y1 - k) / (y2 - k))
    // h = (s*(x2) - x1) / (s - 1)
    public static ParabolicEquationByYTop<Out extends OutParabolicEquation>(out: Out, x1: number, x2: number, y_top: number, y1: number, y2: number) {
        let s = Math.sqrt((y1 - y_top) / (y2 - y_top));
        if(!s) {
            console.error(`Error ! [y_top] should NOT be between [y1] and [y2]! (y1: ${y1}, y2: ${y2}, y_top: ${y_top})`);
            return;
        }
        let h1 = (s * (x2) - x1) / (s - 1);
        let h2 = (-s * (x2) - x1) / (-s - 1);
        let h = h1;
        if((h1 > x1 && h1 > x2) || (h1 < x1 && h1 < x2)) {
            h = h2;
        }
        
        // console.log(`y1: ${y1}, y2: ${y2}, y_top: ${y_top}, h: ${h}`);
        
        out.a = (y1 - y_top) / Math.pow(x1 - h, 2);
        out.b = -2 * out.a * h;
        out.c = out.a * h * h + y_top;
    }

    /** 在折线上取点 */
    public static GetPointOnPath(points: cc.Vec2[], startDistance: number, distance: number): cc.Vec2 {
        if(points.length < 2) return cc.Vec2.ZERO;
        let leftDistance = startDistance + distance;
        let startPos = points[0];
        let lineEndPos = points[1];
        for(let i = 0; i < points.length - 1; i++) {
            startPos = points[i];
            lineEndPos = points[i + 1];
            let lineDistance = startPos.sub(lineEndPos).len();
            if(leftDistance <= lineDistance) {
                break;
            }
            leftDistance -= lineDistance;
        }
        return startPos.add(lineEndPos.sub(startPos).normalize().mul(leftDistance));
    }
    
    /**
     * 线段与线段的交点
     * @param {boolean} isBordered_l1 l1 是否是有边界的线段
     * @param {boolean} isBordered_l2 l2 是否是有边界的线段
     * @returns {cc.Vec2[]} 
     */
    public static LineCrossLine(l1_x1: number, l1_y1: number, l1_x2: number, l1_y2: number, l2_x1: number, l2_y1: number, l2_x2: number, l2_y2: number
              , isBordered_l1: boolean = false, isBordered_l2: boolean = false): cc.Vec2 {
        if(l1_x1 == l2_x1 && l1_y1 == l2_y1 || l1_x1 == l2_x2 && l1_y1 == l2_y2) return cc.v2(l1_x1, l1_y1);
        if(l1_x2 == l2_x1 && l1_y2 == l2_y1 || l1_x2 == l2_x2 && l1_y2 == l2_y2) return cc.v2(l1_x2, l1_y2);
        let result: cc.Vec2 = null;
        if(l1_x1 - l1_x2 == 0) {
            if(l2_x1 - l2_x2 == 0) {
                return null;
            } else {
                let k2 = (l2_y2 - l2_y1) / (l2_x2 - l2_x1);
                let b2 = l2_y1 - k2 * l2_x1;
                result = cc.v2(l1_x1, k2 * l1_x1 + b2);
            }
        } else if(l2_x1 - l2_x2 == 0) {
            let k1 = (l1_y2 - l1_y1) / (l1_x2 - l1_x1);
            let b1 = l1_y1 - k1 * l1_x1;
            result = cc.v2(l2_x1, k1 * l2_x1 + b1);
        } else {
            let k1 = (l1_y2 - l1_y1) / (l1_x2 - l1_x1);
            let b1 = l1_y1 - k1 * l1_x1;
            let k2 = (l2_y2 - l2_y1) / (l2_x2 - l2_x1);
            let b2 = l2_y1 - k2 * l2_x1;
            if(k1 == k2) {
                return null;
            } else {
                let x = (b2 - b1) / (k1 - k2);
                let y = k1 * x + b1;
                result = cc.v2(x, y);
            }
        }
        if(isBordered_l1 && result) {
            if((l1_x1 < l1_x2) && (result.x >= l1_x1 && result.y <= l1_x2) || (l1_x1 > l1_x2) && (result.x <= l1_x1 && result.x >= l1_x2)
                  || (l1_y1 < l1_y2) && (result.y >= l1_y1 && result.y <= l1_y2) || (l1_y1 > l1_y2) && (result.y <= l1_y1 && result.y >= l1_y2)) {
                // result = null;
            } else {
                result = null;
            }
        }
        if(isBordered_l2 && result) {
            if((l2_x1 < l2_x2) && (result.x >= l2_x1 && result.y <= l2_x2) || (l2_x1 > l2_x2) && (result.x <= l2_x1 && result.x >= l2_x2)
                  || (l2_y1 < l2_y2) && (result.y >= l2_y1 && result.y <= l2_y2) || (l2_y1 > l2_y2) && (result.y <= l2_y1 && result.y >= l2_y2)) {
                // result = null;
            } else {
                result = null;
            }
        }
        return result;
    }

    /**
     * 计算线段与圆的交点
     * 线段1：x1, y1, x2, y2
     * 圆：xo, yo, r
     * @param {boolean} isBordered 是否是有边界的线段
     * @returns {cc.Vec2[]} 
     */
    public static LineCrossCircle(x1: number, y1: number, x2: number, y2: number, xo: number, yo: number, r: number, isBordered: boolean = false): cc.Vec2[] {
        // 直线方程：y = kx + b;  圆方程：(x - xo)^2 + (y - yo)^2 = r^2;
        // 代入：x^2 - 2 * xo * x + xo^2 + (k * x + b)^2 - 2 * (k * x + b) * yo + yo^2 - r^2 = 0;
        // 化简：(k^2 + 1) * x^2 + 2 * (k * (b - yo) - xo) * x + (xo^2 + (b - yo)^2 - r^2) = 0;
        let resultX = 0;
        let resultY = 0;
        let resultX2 = 0;
        let resultY2 = 0;
        let d = -1;
        if(x1 == x2) {
            d = r * r - (x1 - xo) * (x1 - xo);
            if(d < 0) {
                return [];
            } else if(d == 0) {
                return [cc.v2(x1, yo)];
            } else {
                resultX = x1;
                resultY = yo + Math.sqrt(d);
                resultX2 = x1;
                resultY2 = yo - Math.sqrt(d);
            }
        } else {
            let line_k = (y1 - y2) / (x1 - x2);
            let line_b = y1 - line_k * x1;
            let a = line_k * line_k + 1;
            let b = 2 * (line_k * (line_b - yo) - xo);
            let c = xo * xo + (line_b - yo) * (line_b - yo) - r * r;
            let d = b * b - 4 * a * c;
            if(d < 0) {
                return []; // 无交点
            }
            resultX = (-b - Math.sqrt(d)) / (2 * a);
            resultY = line_k * resultX + line_b;
            resultX2 = (-b + Math.sqrt(d)) / (2 * a);
            resultY2 = line_k * resultX2 + line_b;
        }
        let result: cc.Vec2[] = []; 
        if(d == 0) { // 只有一个交点
            if(isBordered) {
                if((x1 < x2) && (resultX >= x1 && resultX <= x2) || (x1 >= x2) && (resultX <= x1 && resultX >= x2)) {
                    result.push(cc.v2(resultX, resultY));
                }
            } else {
                result.push(cc.v2(resultX, resultY));
            }
        } else { // 有两个交点
            let distance1 = cc.v2(x1, y1).sub(cc.v2(resultX, resultY)).len();
            let distance2 = cc.v2(x1, y1).sub(cc.v2(resultX2, resultY2)).len();
            if(distance1 > distance2) { // 如果第一个交点离起点更远，则交换两个交点
                let temp = resultX;
                resultX = resultX2;
                resultX2 = temp;
                temp = resultY;
                resultY = resultY2;
                resultY2 = temp;
            }
            if(isBordered) {
                if((x1 < x2) && (resultX >= x1 && resultX <= x2) || (x1 > x2) && (resultX <= x1 && resultX >= x2)
                      || (y1 < y2) && (resultY >= y1 && resultY <= y2) || (y1 > y2) && (resultY <= y1 && resultY >= y2)) {
                    result.push(cc.v2(resultX, resultY));
                }
                if((x1 < x2) && (resultX2 >= x1 && resultX2 <= x2) || (x1 > x2) && (resultX2 <= x1 && resultX2 >= x2)
                      || (y1 < y2) && (resultY2 >= y1 && resultY2 <= y2) || (y1 > y2) && (resultY2 <= y1 && resultY2 >= y2)) {
                    result.push(cc.v2(resultX2, resultY2));
                }
            } else {
                result.push(cc.v2(resultX, resultY));
                result.push(cc.v2(resultX2, resultY2));
            }
        }
        return result;
    }

    /**
     * 计算点到线段的距离
     * x0, y0 点坐标，x1, y1, x2, y2 线段两端坐标
     * @param {boolean} isBordered 是否是线段
     * @returns {number} 
     */
    public static DistancePointToLine(x0: number, y0: number, x1: number, y1: number, x2: number, y2: number, isBordered: boolean = false): number {
        // let A = cc.v2(x0, y0);
        // let B = cc.v2(x1, y1);
        // let C = cc.v2(x2, y2);
        // let BA = A.sub(B);
        // let BC = C.sub(B);
        // let h = BA.dot(BC) / BC.len();
        // let D = B.add(BC.normalize().mul(h));
        // if(isBordered) {
        //     if(h < 0) {
        //         return A.sub(B).len();
        //     }
        //     if(h > BC.len()) {
        //         return A.sub(C).len();
        //     }
        // } else {
        //     return A.sub(D).len();
        // }
        let closestPoint = this.ClosestPointToLine(cc.v2(x0, y0), cc.v2(x1, y1), cc.v2(x2, y2), isBordered);
        return cc.v2(x0, y0).sub(closestPoint).len();
    }

    // 计算点移动到线段上的最近点
    public static ClosestPointToLine(outPoint: cc.Vec2, lineStart: cc.Vec2, lineEnd: cc.Vec2, isBordered: boolean = false): cc.Vec2 {
        let BA = outPoint.sub(lineStart);
        let BC = lineEnd.sub(lineStart);
        let h = BA.dot(BC) / BC.len();
        let D = lineStart.add(BC.normalize().mul(h));
        if(isBordered) {
            if(h < 0) {
                return lineStart;
            }
            if(h > BC.len()) {
                return lineEnd;
            }
        }
        return D;
    }

    // 计算多边形外一点移动到多边形的边缘最近的点坐标
    public static ClosestPointToPolygon(outPoint: cc.Vec2, points: cc.Vec2[]): cc.Vec2 {
        let minDistance = Number.MAX_VALUE;
        let closestPointToPolygon = cc.Vec2.ZERO;
        for(let i = 0; i < points.length; i++) {
            let nextIndex = (i + 1) % points.length;
            let closestPointToLine = this.ClosestPointToLine(outPoint, points[i], points[nextIndex], true);
            let distance = outPoint.sub(closestPointToLine).len();
            if(distance < minDistance) {
                minDistance = distance;
                closestPointToPolygon = closestPointToLine;
            }
        }
        return closestPointToPolygon;
    }

    // 贝塞尔曲线拟合圆弧
    // 公式： h = (4/3) * (1 - cos(x/2)) / sin(x/2)
    // 当 x = 90度时，得到 h = 0.522...
    
    public static Vec2ToAngle(vector: cc.Vec2, baseVector = cc.v2(1, 0)): number {
        if(vector.equals(cc.v2(0, 0))) vector = cc.v2(1, 0);
        if(baseVector.equals(cc.v2(0, 0))) baseVector = cc.v2(1, 0);
        return baseVector.signAngle(vector) * 180 / Math.PI;
    }
    
    public static AngleToVec2(angle: number, baseVector = cc.v2(1, 0)): cc.Vec2 {
        if(baseVector.equals(cc.v2(0, 0))) baseVector = cc.v2(1, 0);
        return baseVector.rotate(angle * Math.PI / 180);
    }

    public static ColorFromHex(out: cc.Color, hex: number, isIncludeAlpha = false): cc.Color {
        // 用法：
        // let color = cc.Color.WHITE;
        // LocalUtils.ColorFromHex(color, 0xeb9b19);
        let r = 0;
        let g = 0;
        let b = 0;
        let a = 0;
        if(isIncludeAlpha) {
            r = (hex >> 24);
            g = (hex >> 16) & 0xff;
            b = (hex >> 8) & 0xff;
            a = (hex) & 0xff;
            out.a = a;
        } else {
            r = (hex >> 16) & 0xff;
            g = (hex >> 8) & 0xff;
            b = (hex) & 0xff;
        }
        out.r = r;
        out.g = g;
        out.b = b;
        return out;
    }

    public static GetRectPosFromDir(dir: cc.Vec2, width: number, height: number, OutMargin = 0) {
        let widthScale = Math.abs((width/2 + OutMargin) / dir.normalize().x);
        let heightScale = Math.abs((height/2 + OutMargin) / dir.normalize().y);
        let scale = widthScale < heightScale ? widthScale : heightScale;
        let pos = dir.normalize().mul(scale);
        return pos;
    }

}

export class TweenObject<T> {
    get value(): T {
        return this._value;
    }

    set value(v: T) {
        this._callback(v);
        this._value = v;
    }

    private _callback: Function = ()=>{};
    private _value: T;

    constructor(value: T, callback: Function) {
        this._value = value;
        this._callback = callback;
    }
}

export interface OutParabolicEquation {
    a: number;
    b: number;
    c: number;
}

export interface ShakerData {
    srcX?: number;       //[[初始位置x]]
    srcY?: number;       //[[初始位置y]]
    offsetMaxX?: number;     //[[最大偏移量x]]
    offsetMaxY?: number;     //[[最大偏移量y]]
    offsetRangeRatio?: number;
    // offsetRangeX?: number;     //[[偏移量波动范围x]]
    // offsetRangeY?: number;     //[[偏移量波动范围y]]
    interval?: number;  //[[震动频率]]
    totalTime?: number;    //[[震动时间]]
}

export type StringDictionary<T> = {
    [str: string] : T;
}

type Override<P, S> = Omit<P, keyof S> & S;
type ParamType<T> = T extends (param: infer P) => any ? P : T;