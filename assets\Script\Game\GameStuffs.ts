// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import Util from "../Base/Util";
import GameManager from "../GameManager";
import GameUtils from "../GameUtils";
import LocalUtils from "../LocalUtils";
import Building from "./Building";
import GameDirector from "./GameDirector";
import GameStuffManager from "./GameStuffManager";
import { ResourceType } from "./Resource";
import Stuff from "./Stuff";
import Tower from "./Tower";

const {ccclass, property} = cc._decorator;

@ccclass
export default class GameStuffs extends cc.Component {

    @property(cc.Node)
    smoke_make: cc.Node = null;
    @property(cc.Node)
    zhuozi_2: cc.Node = null;

    @property([cc.Node])
    newUnlockNodes_1: cc.Node[] = [];
    @property([cc.Node])
    newUnlockNodes_2: cc.Node[] = [];
    @property([cc.Node])
    newUnlockNodes_3: cc.Node[] = [];
    @property([cc.Node])
    newUnlockNodes_4: cc.Node[] = [];
    @property([cc.Node])
    newUnlockNodes_5: cc.Node[] = [];
    @property([cc.Node])
    newUnlockNodes_6: cc.Node[] = [];
    @property([cc.Node])
    newUnlockNodes_7: cc.Node[] = [];

    @property([cc.Node])
    floors: cc.Node[] = [];

    // @property([cc.Node])
    // floor_guides: cc.Node[] = [];

    @property(cc.PhysicsPolygonCollider)
    trigger_area_stripLog: cc.PhysicsPolygonCollider = null;
    @property(cc.PhysicsPolygonCollider)
    trigger_area_stripStone: cc.PhysicsPolygonCollider = null;
    @property(cc.PhysicsPolygonCollider)
    trigger_area_groundMeat: cc.PhysicsPolygonCollider = null;

    @property(Building)
    guidePoint: Building = null;

    private _isStripStoneCreatorRunning = false;
    private _stripStoneCreatorCreateTime = 0;
    private _stripStoneCreatorCreateTimeInterval = 8;

    private _recreateResourceTime = 0;

    private _createWeaponTime = 0;
    private _createWeaponTimeInterval = 3;

    isCreateWeapon: boolean[] = [false, false, false, false];

    // LIFE-CYCLE CALLBACKS:

    onLoad () {
        GameUtils.rootGameStuffs = this;
    }

    start () {
        GameManager.instance.AddGameUpdate('GameStuffs', (dt: number)=>{
            this.gameUpdate(dt);
        });

        // this.StartCreateStripStone();
    }

    gameUpdate(dt: number) {
        // this.TryStartCreateStripStone();
        // this.StripStoneCreatorUpdate(dt);
        // this.TryRecreateTrees();

        // this.RefreshFloorGuide();
        
        // if(this._recreateResourceTime < 1.2) {
        //     this._recreateResourceTime += dt;
        // } else if(this.TryRecreateResource()) {
        //     this._recreateResourceTime = 0;
        // }
        // let workbenchResourceNum = 0;
        // workbenchResourceNum += GameUtils.rootGameWorld.coinStorageArea.GetNumOfResource().resourceNums[ResourceType.coin];
        // workbenchResourceNum += GameUtils.rootGameWorld.meatStorageArea.GetNumOfResource().resourceNums[ResourceType.groundMeat];
        // workbenchResourceNum += GameUtils.rootGameWorld.cookStorageArea.GetNumOfResource().resourceNums[ResourceType.groundMeat];
        // workbenchResourceNum += GameUtils.rootGameWorld.cookStorageArea.GetNumOfResource().resourceNums[ResourceType.food];
        // workbenchResourceNum += GameUtils.rootGameWorld.buyFoodStorageArea.GetNumOfResource().resourceNums[ResourceType.food];
        // let minCost = 999999;
        // GameUtils.rootGameWorld.floorUnlockAreaList.forEach((e)=>{
        //     if(e.isCanUnlock && !e.isUnlockFinished) {
        //         if(e.leftCost < minCost) {
        //             minCost = e.leftCost;
        //         }
        //     }
        // });
        // if(workbenchResourceNum == 0 && GameUtils.GetResourceNum(ResourceType.coin) < minCost) {
        //     GameDirector.instance.OnWorkbenchEmptyAndNotEnoughCoin();
        // } else if(workbenchResourceNum > 0 || GameUtils.GetResourceNum(ResourceType.coin) >= minCost) {
        //     GameDirector.instance.OnWorkbenchNotEmpty();
        // }
        // if(!GameDirector.instance.isArriveGuidePoint) {
        //     let distance = GameUtils.mainHero.script.rootPosition.sub(this.guidePoint.rootPosition).len();
        //     if(distance < this.guidePoint.unlockDistance) {
        //         GameDirector.instance.isArriveGuidePoint = true;
        //     }
        // }
        // if(!GameDirector.instance.isFirstCoinEnough) {
        //     let coinNum = GameUtils.mainHeroBackpack.GetNumOfResource().resourceNums[ResourceType.coin];
        //     if(coinNum >= 20) {
        //         GameDirector.instance.OnFirstCoinEnough();
        //     }
        // }
        LocalUtils.ArrayForEach(this.isCreateWeapon, (e, i)=>{
            if(e) {
                this._createWeaponTime[i] += dt;
                if(this._createWeaponTime[i] > this._createWeaponTimeInterval) {
                    if(this.TryCreateWeaponResource()) {
                        this._createWeaponTime[i] = 0;
                    }
                }
            }
        });
        [].forEach
        if(this.isCreateBow) {
            this._createBowTime += dt;
            if(this._createBowTime > this._createBowTimeInterval) {
                if(this.TryCreateWeaponResource()) {
                    // this._isCreateBow = true;
                    this._createBowTime = 0;
                }
            }
        }
    }

    Init() {
        // this.HideAllNodes();
        // this.ShowNodes(1);
    }

    HideAllNodes() {
        this.HideNodes(1);
        this.HideNodes(2);
        this.HideNodes(3);
        this.HideNodes(4);
        this.HideNodes(5);
        this.HideNodes(6);
        this.HideNodes(7);
        this.floors[1].active = false;
        this.floors[2].active = false;
        this.floors[3].active = false;
        this.floors[4].active = false;
        this.floors[5].active = false;
        this.floors[6].active = false;
        this.floors[7].active = false;
    }

    HideNodes(nodesRef: number) {
        let nodes = [];
        switch(nodesRef) {
            case 1:
                nodes = this.newUnlockNodes_1;
                break;
            case 2:
                nodes = this.newUnlockNodes_2;
                break;
            case 3:
                nodes = this.newUnlockNodes_3;
                break;
            case 4:
                nodes = this.newUnlockNodes_4;
                break;
            case 5:
                nodes = this.newUnlockNodes_5;
                break;
            case 6:
                nodes = this.newUnlockNodes_6;
                break;
            case 7:
                nodes = this.newUnlockNodes_7;
                break;
            default:
                break;
        }
        nodes.forEach((e)=>{ e.active = false; });
    }

    ShowNodes(nodesRef: number) {
        let nodes = [];
        switch(nodesRef) {
            case 1:
                nodes = this.newUnlockNodes_1;
                break;
            case 2:
                nodes = this.newUnlockNodes_2;
                break;
            case 3:
                nodes = this.newUnlockNodes_3;
                break;
            case 4:
                nodes = this.newUnlockNodes_4;
                break;
            case 5:
                nodes = this.newUnlockNodes_5;
                break;
            case 6:
                nodes = this.newUnlockNodes_6;
                break;
            case 7:
                nodes = this.newUnlockNodes_7;
                break;
            default:
                break;
        }
        nodes.forEach((e)=>{
            let tower = e.getComponent(Tower);
            if(tower) {
                tower.Unlock();
            } else {
                let srcScaleX = e.scaleX;
                let srcScaleY = e.scaleY;
                e.scaleX = e.scaleX * 0.3;
                e.scaleY = e.scaleY * 0.3;
                cc.tween(e).to(0.3, {scaleX: srcScaleX, scaleY: srcScaleY}).start();
            }
            e.active = true;
        });
    }

    TryStartCreateStripStone() {
        if(!this._isStripStoneCreatorRunning && GameDirector.instance.isConveyerUnlock) {
            this.StartCreateStripStone();
        }
    }

    StartCreateStripStone() {
        this._isStripStoneCreatorRunning = true;
        this._stripStoneCreatorCreateTime = 6;
    }

    StripStoneCreatorUpdate(dt: number) {
        if(!this._isStripStoneCreatorRunning) return;
        this._stripStoneCreatorCreateTime += dt;
        if(this._stripStoneCreatorCreateTime > this._stripStoneCreatorCreateTimeInterval) {
            this._stripStoneCreatorCreateTime = 0;
            GameUtils.rootGameWorld.CreatePushingStuff(cc.v2(GameUtils.rootPathPoints.stripStoneOutPathPoint.position), 2, true, true);
        }
    }

    TryRecreateTrees() {
        let maxNumOfTrees = GameUtils.rootGameWorld.treeLocPoints.length;
        let numOfTrees = 0;
        GameUtils.rootGameWorld.isTreeLocPointUsed.forEach((e)=>{
            if(e) {
                numOfTrees += 1;
            }
        });
        if(numOfTrees < maxNumOfTrees - 2) {
            let randomIndex = Math.floor(Math.random() * maxNumOfTrees);
            if(GameUtils.rootGameWorld.isTreeLocPointUsed[randomIndex] || randomIndex == GameUtils.rootGameWorld.lastCutTreeLocPointIndex) {
                return;
            }
            let script = GameUtils.rootGameWorld.CreateATree(cc.v2(GameUtils.rootGameWorld.treeLocPoints[randomIndex].position));
            GameUtils.rootGameWorld.gatheringBuildingList.push(script);
            script.treeLocPointIndex = randomIndex;
            GameUtils.rootGameWorld.isTreeLocPointUsed[randomIndex] = true;
        }
    }

    // TryRecreateResource() {
    //     let nowMeatTableNpcNum = GameDirector.instance.nowMeatTableNpcNum;
    //     let resourceNum = GameStuffManager.instance.resourceLists[ResourceType.groundMeat].length;
    //     let timeDelay = 0.2;
    //     if(resourceNum < 50) {
    //         this.PlayMeatTableNpcAnim(0);
    //         GameManager.instance.LateTimeCallOnce(()=>{
    //             this.RecreateResource();
    //         }, timeDelay);
    //         for(let i = 1; i < nowMeatTableNpcNum; i++) {
    //             GameManager.instance.LateTimeCallOnce(()=>{
    //                 this.PlayMeatTableNpcAnim(i);
    //             }, i * 0.4);
    //             GameManager.instance.LateTimeCallOnce(()=>{
    //                 this.RecreateResource();
    //             }, timeDelay + i * 0.4);
    //         }
    //         return true;
    //     }
    //     return false;
    // }

    PlayMeatTableNpcAnim(npcIndex: number) {
        let meatTableNode = GameUtils.rootGameWorld.building_meatTable.node;
        let npcSpine = meatTableNode.getChildByName('npc_' + (npcIndex + 1)).getComponent(sp.Skeleton);
        let trackEntry: sp.spine.TrackEntry = npcSpine.setAnimation(0, 'dig', false);
        npcSpine.setTrackCompleteListener(trackEntry, ()=>{
            npcSpine.setAnimation(0, 'idle', true);
        });
    }

    RecreateResource() {
        let centerPos = cc.v2(-600, -200);
        let randomDistance = Math.random() * 350 + 220;
        let randomDir = LocalUtils.AngleToVec2(Util.RandomRange(-70, 30));
        let posOffset = randomDir.normalize().mul(randomDistance);
        let info = GameStuffManager.instance.CreateDropResource(centerPos, posOffset, ResourceType.groundMeat);
        info.script.MoveToGroundCanPush(0.6);
        GameStuffManager.instance.resourceLists[GameUtils.GetResourceIDByType(ResourceType.groundMeat)].push(info);
    }

    // RefreshFloorGuide() {
    //     if(!GameDirector.instance.isNpcUnlock && GameStuffManager.instance.mainStripLog && !GameStuffManager.instance.mainStripLog.isInPlace) {
    //         this.floor_guides[0].active = true;
    //     } else {
    //         this.floor_guides[0].active = false;
    //     }
    //     if(GameStuffManager.instance.mainStripStone && GameStuffManager.instance.mainStripStone.isBornComplited && !GameStuffManager.instance.mainStripStone.isInPlace) {
    //         this.floor_guides[1].active = true;
    //     } else {
    //         this.floor_guides[1].active = false;
    //     }
    // }

    TryCreateWeaponResource() {
        let bowNum = GameUtils.rootGameWorld.bowStorageArea.GetNumOfResource().resourceNums[ResourceType.bow];
        if((bowNum < 5 || GameDirector.instance.isConveyerUnlock) && GameDirector.instance.isBowMakerCreated) {
            console.log('生产弓！');
            GameStuffManager.instance.CreateBow(cc.v2(-750, -1000));
            return true;
        }
        return false;
    }
}
