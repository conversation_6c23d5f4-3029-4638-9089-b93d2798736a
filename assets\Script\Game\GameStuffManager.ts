// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import Util from "../Base/Util";
import Unit from "../Game/Unit";
import GameManager from "../GameManager";
import GameUtils, { UnitInfo } from "../GameUtils";
import LocalUtils, { TweenObject } from "../LocalUtils";
import AntiExtrusionManager from "./AntiExtrusionManager";
import Building from "./Building";
import Bullet from "./Bullet";
import Enemy from "./Enemy";
import GameDirector from "./GameDirector";
import GatheringBuilding from "./GatheringBuilding";
import NPC from "./NPC";
import PushingStuff from "./PushingStuff";
import Resource, { ResourceInfo, ResourceState, ResourceType } from "./Resource";
import StorageArea from "./StorageArea";
import UnlockProgressUI from "./UnlockProgressUI";


export default class GameStuffManager {
    /** 单例实例 */
    public static get instance(): GameStuffManager {
        if(!this._instance) {
            this._instance = new GameStuffManager();
            this._instance.InitAtInstantiation();
        }
        return this._instance;
    }
    private static _instance: GameStuffManager = null;

    get MiddleNode(): cc.Node {
        return GameUtils.rootGameWorld.MiddleNode;
    }

    get resourcePrefabs(): cc.Prefab[] {
        return GameUtils.rootGameWorld.resourcePrefabs;
    }

    get stockades(): Building[] {
        return GameUtils.rootGameWorld.stockades;
    }
    // public static mainHero: Hero;
    mainHero: UnitInfo;
    mainBoss: UnitInfo;
    heroList: UnitInfo[] = [];
    enemyList: UnitInfo[] = [];
    npcList: UnitInfo[] = [];
    npcLoggerList: UnitInfo[] = [];
    npcBuyerList: UnitInfo[] = [];
    npcSoldierList: UnitInfo[] = [];

    buyerList: NPC[] = [];
    soldierList: NPC[] = [];
    worker: NPC = null;
    mainStripLog: PushingStuff = null;
    mainStripStone: PushingStuff = null;
    stripStones: PushingStuff[] = [];

    bulletList: Bullet[] = [];
    
    resourceLists: ResourceInfo[][] = [];


    InitAtInstantiation() {
        this.InitResourceLists();
    }

    InitResourceLists() {
        this.resourceLists[GameUtils.GetResourceIDByType(ResourceType.coin)] = [];
        this.resourceLists[GameUtils.GetResourceIDByType(ResourceType.bow)] = [];
        this.resourceLists[GameUtils.GetResourceIDByType(ResourceType.wood2)] = [];
        this.resourceLists[GameUtils.GetResourceIDByType(ResourceType.rawMeat)] = [];
        this.resourceLists[GameUtils.GetResourceIDByType(ResourceType.food)] = [];
        this.resourceLists[GameUtils.GetResourceIDByType(ResourceType.groundMeat)] = [];
    }

    Init() {}

    FindAEnemy(pos: cc.Vec2) {
        let minDistance = -1;
        let enemy: UnitInfo = null;
        this.enemyList.forEach((e)=>{
            if(!e.script.isAlive) {
                return;
            }
            // let distance = e.script.rootPosition.sub(pos).len();
            let distance = GameUtils.Fake3dDistanceExpand(e.script.rootPosition, pos);
            if(minDistance < 0 || distance < minDistance) {
                minDistance = distance;
                enemy = e;
            }
        });
        if(!enemy) {
            // console.error(`enemy: ${enemy}`);
            return null;
        }
        
        return enemy;
    }

    TryGetEnemysInArea(pos: cc.Vec2, radius: number) {
        let enemys: UnitInfo[] = [];
        this.enemyList.forEach((e)=>{
            // let distance = e.script.rootPosition.sub(pos).len();
            let distance = GameUtils.Fake3dDistanceExpand(e.script.rootPosition, pos);
            if(distance < radius) {
                enemys.push(e);
            }
        });
        return enemys;
    }

    GetEnemysInPolygon(points: cc.Vec2[]) {
        let enemys: UnitInfo[] = [];
        this.enemyList.forEach((e)=>{
            let pos = e.script.rootPosition;
            let isInPolygon = cc.Intersection.pointInPolygon(pos, points);
            if(isInPolygon) {
                enemys.push(e);
            }
        });
        return enemys;
    }

    FindAGatheringBuilding(pos: cc.Vec2, gatheringBuildingRef: number = -1) {
        let minDistance = -1;
        let gatheringBuilding: GatheringBuilding = null;
        GameUtils.rootGameWorld.gatheringBuildingList.forEach((e)=>{
            if(!e.isCanGather) {
                return;
            }
            // let distance = e.script.rootPosition.sub(pos).len();
            let distance = GameUtils.Fake3dDistanceExpand(e.rootPosition, pos);
            // console.log(`distance: ${distance}`);
            
            if(minDistance < 0 || distance < minDistance) {
                if(gatheringBuildingRef == -1 || e.gatheringBuildingRef == gatheringBuildingRef) {
                    minDistance = distance;
                    gatheringBuilding = e;
                }
            }
        });
        if(!gatheringBuilding) {
            // console.error(`enemy: ${enemy}`);
            return null;
        }
        
        return gatheringBuilding;
    }

    FindANPCLogger(pos: cc.Vec2) {
        let minDistance = -1;
        let npc: UnitInfo = null;
        this.npcLoggerList.forEach((e)=>{
            if(!e.script.isAlive) {
                return;
            }
            let distance = GameUtils.Fake3dDistanceExpand(e.script.rootPosition, pos);
            if(minDistance < 0 || distance < minDistance) {
                minDistance = distance;
                npc = e;
            }
        });
        if(!npc) {
            return null;
        }
        
        return npc;
    }

    FindAStockade(pos: cc.Vec2, filter: (stockade: Building) => boolean = null) {
        let minDistance = -1;
        let stockade: Building = null;
        this.stockades.forEach((e)=>{
            if(!e) return;
            if(filter && !filter(e)) return;
            let distance = GameUtils.Fake3dDistanceExpand(e.rootPosition, pos);
            if(minDistance < 0 || distance < minDistance) {
                minDistance = distance;
                stockade = e;
            }
        });
        if(!stockade) {
            return null;
        }
        
        return stockade;
    }

    FindAUnlockedStockade(pos: cc.Vec2) {
        let minDistance = -1;
        let stockade: Building = null;
        this.stockades.forEach((e)=>{
            if(!e) return;
            if(!(e.statueRef == 101 || e.statueRef == 102) || !e.isStockadeUnlock) {
                return;
            }
            let distance = GameUtils.Fake3dDistanceExpand(e.rootPosition, pos);
            if(minDistance < 0 || distance < minDistance) {
                minDistance = distance;
                stockade = e;
            }
        });
        if(!stockade) {
            return null;
        }
        
        return stockade;
    }

    TryGetGatheringBuildingInArea(pos: cc.Vec2, radius: number, gatheringBuildingRef: number = -1) {
        let gatheringBuildings: GatheringBuilding[] = [];
        GameUtils.rootGameWorld.gatheringBuildingList.forEach((e)=>{
            // let distance = e.script.rootPosition.sub(pos).len();
            let distance = GameUtils.Fake3dDistanceExpand(e.rootPosition, pos);
            if(distance < radius) {
                if(gatheringBuildingRef == -1 || e.gatheringBuildingRef == gatheringBuildingRef) {
                    gatheringBuildings.push(e);
                }
            }
        });
        return gatheringBuildings;
    }

    PushEnemyInfo(info: UnitInfo) {
        this.enemyList.push(info);
    }

    PushGatheringBuilding(gatheringBuilding: GatheringBuilding) {
        GameUtils.rootGameWorld.gatheringBuildingList.push(gatheringBuilding);
    }

    PushBullet(bullet: Bullet) {
        this.bulletList.push(bullet);
    }

    PushNPCInfo(info: UnitInfo, NPCRef: number) {
        if(NPCRef == 1) {
            this.npcSoldierList.push(info);
            this.npcList.push(info);
        } else if(NPCRef == 2) {
            this.npcLoggerList.push(info);
        } else if(NPCRef == 3) {
            this.npcLoggerList.push(info);
        } else if(NPCRef == 4) {
            this.npcBuyerList.push(info);
        }
    }

    RemoveEnemy(unit_id: number) {
        let enemyIndex = this.enemyList.findIndex((e)=>{
            return e.script.unit_id == unit_id;
        });
        let enemy = this.enemyList[enemyIndex].script as Enemy;
        if(enemy.enemyRef == 7) {
            if(this.mainBoss == this.enemyList[enemyIndex]) {
                // GameUtils.rootLayer_1.HideBossComing();
                // GameUtils.rootEnemyCreator.AllowCreateBoss();
                this.mainBoss = null;
            }
        }
        if(enemyIndex >= 0) {
            this.enemyList.splice(enemyIndex, 1);
        }
    }

    RemoveGatheringBuilding(gatheringBuilding_id: number) {
        let gatheringBuildingIndex = GameUtils.rootGameWorld.gatheringBuildingList.findIndex((e)=>{
            return e.gatheringBuilding_id == gatheringBuilding_id;
        });
        let gatheringBuilding = GameUtils.rootGameWorld.gatheringBuildingList[gatheringBuildingIndex] as GatheringBuilding;
        if(gatheringBuildingIndex >= 0) {
            GameUtils.rootGameWorld.gatheringBuildingList.splice(gatheringBuildingIndex, 1);
            GameUtils.rootGameWorld.isTreeLocPointUsed[gatheringBuilding.treeLocPointIndex] = false;
            GameUtils.rootGameWorld.lastCutTreeLocPointIndex = gatheringBuilding.treeLocPointIndex;
        } else {
            console.log('错误！未找到要移除的资源建筑！');
        }
    }

    RemoveBullet(bullet: Bullet) {
        let index = this.bulletList.findIndex((e)=>{
            return e.stuff_id == bullet.stuff_id;
        });
        if(index >= 0) {
            this.bulletList.splice(index, 1);
            AntiExtrusionManager.instance.antiExtrusionMatch.OnBulletRemove(bullet);
        }
    }

    RemoveNPCLogger(unit_id: number) {
        let index = this.npcLoggerList.findIndex((e)=>{
            return e.script.unit_id == unit_id;
        });
        let npc = this.npcLoggerList[index].script as NPC;
        if(npc.npcRef == 2) {
            
        }
        if(index >= 0) {
            this.npcLoggerList.splice(index, 1);
        }
    }

    RemoveNPCSoldier(unit_id: number) {
        let index = this.npcSoldierList.findIndex((e)=>{
            return e.script.unit_id == unit_id;
        });
        let npc = this.npcSoldierList[index].script as NPC;
        if(npc.npcRef == 1) {
            
        }
        if(index >= 0) {
            this.npcSoldierList.splice(index, 1);
        }
    }

    BuyerListIn(NPCBuyer: NPC) {
        return this.buyerList.push(NPCBuyer) - 1;
    }

    BuyerListOut(NPCBuyer: NPC) {
        let foundBuyerIndex = this.buyerList.findIndex((e)=>{
            return e.unit_id == NPCBuyer.unit_id;
        });
        if(this.buyerList.length != 0 && foundBuyerIndex >= 0) {
            // this.buyerList.shift();
            this.buyerList.splice(foundBuyerIndex, 1);
            this.buyerList.forEach((e, index)=>{
                e.ResetNewBuyerListIndex(index);
            });
        } else {
            // console.error(`要出队列的 Buyer 坐标不是 0 ！`);
            console.log(`要出队列的 Buyer 未找到！`);
        }
    }

    RemoveBuyer(unit_id: number) {
        let index = this.npcBuyerList.findIndex((e)=>{
            return e.script.unit_id == unit_id;
        });
        // let buyerScript = this.enemyList[index].script as NPC;
        this.npcBuyerList.splice(index, 1);
    }
    
    // SoldierListIn(npcSoldier: NPC) {
    //     return this.soldierList.push(npcSoldier) - 1;
    // }

    // SoldierListOut(npcSoldier: NPC) {
    //     if(this.soldierList.length != 0 && this.soldierList[0] == npcSoldier) {
    //         this.soldierList.shift();
    //         this.soldierList.forEach((e, index)=>{
    //             e.ResetNewSoldierListIndex(index);
    //         });
    //     } else {
    //         console.error(`要出队列的 Buyer 坐标不是 0 ！}`);
    //     }
    // }

    FindAResource(pos: cc.Vec2, resourceType: ResourceType) {
        let minDistance = -1;
        let resource: ResourceInfo = null;
        this.resourceLists[GameUtils.GetResourceIDByType(resourceType)].forEach((e)=>{
            if(!e) return;
            let distance = GameUtils.Fake3dDistanceExpand(e.script.rootPosition, pos);
            if(minDistance < 0 || distance < minDistance) {
                minDistance = distance;
                resource = e;
            }
        });
        if(!resource) {
            return null;
        }
        
        return resource;
    }

    TryGetResourcesInArea(pos: cc.Vec2, radius: number, resourceType: ResourceType) {
        let resources: ResourceInfo[] = [];
        this.resourceLists[GameUtils.GetResourceIDByType(resourceType)].forEach((e)=>{
            let distance = e.script.rootPosition.sub(pos).len();
            if(e.state == ResourceState.onGround && e.isCanPickUp && distance < radius) {
                resources.push(e);
            }
        });
        return resources;
    }

    TryPickUpResources(resourceType: ResourceType, pos: cc.Vec2, radius: number, targetStorageArea?: StorageArea, numLimitValue = -1) {
        let resources: ResourceInfo[] = [];
        if(!targetStorageArea) {
            targetStorageArea = GameUtils.mainHeroBackpack;
        }
        // this.resourceLists[GameUtils.GetResourceIDByType(resourceType)].forEach((e)=>{
        //     if(!e || !e.script || !e.script.rootPosition) {
        //         console.error(e);
        //     }
        //     let distance = e.script.rootPosition.sub(pos).len();
        //     if(e.state == ResourceState.onGround && e.isCanPickUp && distance < radius) {
        //         resources.push(e);
        //     }
        // });
        resources = this.TryGetResourcesInArea(pos, radius, resourceType);
        if(numLimitValue >= 0) {
            resources = resources.slice(0, numLimitValue);
        }
        resources.forEach((e)=>{
            targetStorageArea.ResourcePickup(e);
            this.RemoveResourceFromList(e);
        });
    }
    
    RemoveResourceFromList(resource: ResourceInfo) {
        resource.isCanPickUp = false;
        let resourceListIndex = GameUtils.GetResourceIDByType(resource.type);
        let foundResourceIndex = this.resourceLists[resourceListIndex].findIndex((e)=>{
            return e == resource;
        });
        if(foundResourceIndex >= 0) {
            this.resourceLists[resourceListIndex].splice(foundResourceIndex, 1);
        }
    }

    CreateDropCoin(pos: cc.Vec2, num: number) {
        let stepRadius = 70;
        for(let i = 0; i < num; i++) {
            GameManager.instance.LateTimeCallOnce(()=>{
                let radius = Math.sqrt(i) * stepRadius;
                let randomDir = LocalUtils.AngleToVec2(Util.RandomRange(-180, 180), cc.v2(0, 1));
                let movePosOffset = randomDir.normalize().mul(radius).scale(cc.v2(1, 0.8));
                let info = this.CreateDropResource(pos, movePosOffset, ResourceType.coin);
                this.resourceLists[GameUtils.GetResourceIDByType(ResourceType.coin)].push(info);
            }, i * 0.02);
        }
    }

    // CreateDropMeat(pos: cc.Vec2, num: number) {
    //     let stepRadius = 70;
    //     let probability = 1;
    //     if(num < 1) {
    //         probability = num;
    //         num = 1;
    //     }
    //     for(let i = 0; i < num; i++) {
    //         GameManager.instance.LateTimeCallOnce(()=>{
    //             let radius = Math.sqrt(i) * stepRadius;
    //             let randomDir = LocalUtils.AngleToVec2(Util.RandomRange(-180, 180), cc.v2(0, 1));
    //             let movePosOffset = randomDir.normalize().mul(radius).scale(cc.v2(1, 0.8));
    //             if(Math.random() < probability) {
    //                 if(!GameDirector.instance.isAllowRawMeatDropCountinue && GameDirector.instance.rawMeatDropCount >= 24) {
    //                     return;
    //                 }
    //                 this.CreateDropResource(pos, movePosOffset, ResourceType.rawMeat, GameUtils.rootGameWorld.rawMeatDropStorageArea);
    //                 GameDirector.instance.rawMeatDropCount += 1;
    //             }
    //         }, i * 0.02);
    //     }
    // }

    CreateDropWood(pos: cc.Vec2, num: number, isToWoodStorageArea = false) {
        let targetStorageArea = GameUtils.mainHeroBackpack;
        if(isToWoodStorageArea) {
            // targetStorageArea = GameUtils.rootGameWorld.woodStorageArea;
            targetStorageArea = null;
        }
        let stepRadius = 70;
        for(let i = 0; i < num; i++) {
            GameManager.instance.LateTimeCallOnce(()=>{
                let radius = Math.sqrt(i + 1) * stepRadius;
                let randomDir = LocalUtils.AngleToVec2(Util.RandomRange(90, 270), cc.v2(0, 1));
                let movePosOffset = randomDir.normalize().mul(radius).scale(cc.v2(1, 0.8)).add(cc.v2(0, -20));
                // this.CreateDropResource(pos, movePosOffset, ResourceType.wood, targetStorageArea);
                let info = this.CreateDropResource(pos, movePosOffset, ResourceType.wood2, targetStorageArea);
                this.resourceLists[GameUtils.GetResourceIDByType(ResourceType.wood2)].push(info);
            }, i * 0.02);
        }
    }

    CreateBow(pos: cc.Vec2, isToConveyer = false, conveyerIndex: number = -1) {
        if(isToConveyer) {
            if(conveyerIndex >= 0 && conveyerIndex < 4) {
                let info = this.CreateAResource(ResourceType.bow, pos);
                let startConveyerPathPointPos = GameUtils.rootPathPoints.GetConveyerPathPointsPos(conveyerIndex)[0];
                let conveyerStorageAreaIndex = (conveyerIndex == 0 || conveyerIndex == 2) ? 0 : 1;
                let conveyerStorageArea = GameUtils.rootGameWorld.conveyerStorageAreas[conveyerStorageAreaIndex];
                GameManager.instance.LateFrameCall(()=>{
                    info.script.FlyToTargetPos(startConveyerPathPointPos, pos, 0);
                    info.script.SetOnArriveCallback(()=>{
                        info.script.MoveOnConveyer(conveyerIndex);
                        info.script.SetOnArrivePathEndCallback(()=>{
                            conveyerStorageArea.FindAGroup(false, info.type).PutInNoSrc(info);
                        });
                    });
                });
            }
        } else {
            let targetStorageArea = GameUtils.rootGameWorld.bowStorageArea;
            this.CreateDropResource(pos, cc.v2(), ResourceType.bow, targetStorageArea, 0);
        }
    }

    CreateDropResource(pos: cc.Vec2, movePosOffset: cc.Vec2, type: ResourceType, targetStorageArea?: StorageArea, pickUpTime = 1) {
        let info = this.CreateAResource(type, pos);
        // if(!movePosOffset.equals(cc.v2())) {
            info.script.DropAndJumpToPos(pos, movePosOffset);
        // }
        if(targetStorageArea) {
            GameManager.instance.LateTimeCallOnce(()=>{
                targetStorageArea.ResourcePickup(info);
                this.RemoveResourceFromList(info);
            }, pickUpTime);
        }
        return info;
    }

    CreateAResource(type: ResourceType, pos: cc.Vec2): ResourceInfo {
        let info = this.GenerateAResource(type, this.MiddleNode, cc.v3(pos));
        info.state = ResourceState.onGround;
        info.isNoSort = true;
        info.script.rootPosition = pos;
        // info.script.Flip(Math.random() > 0.5);
        return info;
    }

    GenerateAResource(type: ResourceType, parent: cc.Node, pos: cc.Vec3) {
        let prefab = GameStuffManager.instance.GetResourcePrefabByType(type);
        let node = LocalUtils.GenerateNode(parent, prefab, pos);
        
        let info = new ResourceInfo(node);
        let script = node.getComponent(Resource);
        script.info = info;
        info.script = script;
        info.type = type;
        return info;
    }

    GetResourcePrefabByType(resourceType: ResourceType) {
        let prefabIndex = -1;
        switch(resourceType) {
            // case ResourceType.money:
            //     prefabIndex = 0;
            //     break;
            // case ResourceType.meat1:
            //     prefabIndex = 1;
            //     break;
            // case ResourceType.meat2:
            //     prefabIndex = 2;
            //     break;
            // case ResourceType.food1:
            //     prefabIndex = 3;
            //     break;
            // case ResourceType.food2:
            //     prefabIndex = 4;
            //     break;
            case ResourceType.wood:
                prefabIndex = 0;
                break;
            case ResourceType.meat:
                prefabIndex = 1;
                break;
            case ResourceType.coin:
                prefabIndex = 2;
                break;
            case ResourceType.bow:
                prefabIndex = 3;
                break;
            case ResourceType.wood2:
                prefabIndex = 4;
                break;
            case ResourceType.rawMeat:
                prefabIndex = 5;
                break;
            case ResourceType.food:
                prefabIndex = 6;
                break;
            case ResourceType.groundMeat:
                prefabIndex = 7;
                break;
            case ResourceType.sword:
                prefabIndex = 8;
                break;
        }
        if(prefabIndex >= 0) {
            return this.resourcePrefabs[prefabIndex];
        } else {
            return null;
        }
    }
}
